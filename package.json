{"name": "scenic", "version": "1.0.0", "private": true, "description": "景区小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"server:h5": "http-server ./dist/h5/ --port 3000", "build:weapp": "taro build --type weapp --mode prod", "build:swan": "taro build --type swan --mode prod", "build:alipay": "taro build --type alipay --mode prod", "build:tt": "taro build --type tt --mode prod", "build:h5": "taro build --type h5", "build2:h5": "taro build --type h5 --mode prod", "build:rn": "taro build --type rn --mode prod", "build:qq": "taro build --type qq --mode prod", "build:jd": "taro build --type jd --mode prod", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@iconify-json/ic": "^1.1.17", "@iconify-json/iconamoon": "^1.1.1", "@react-google-maps/api": "^2.19.3", "@tarojs/components": "3.6.16", "@tarojs/helper": "3.6.16", "@tarojs/plugin-framework-react": "3.6.16", "@tarojs/plugin-platform-alipay": "3.6.16", "@tarojs/plugin-platform-h5": "3.6.16", "@tarojs/plugin-platform-jd": "3.6.16", "@tarojs/plugin-platform-qq": "3.6.16", "@tarojs/plugin-platform-swan": "3.6.16", "@tarojs/plugin-platform-tt": "3.6.16", "@tarojs/plugin-platform-weapp": "3.6.16", "@tarojs/react": "3.6.16", "@tarojs/runtime": "3.6.16", "@tarojs/shared": "3.6.16", "@tarojs/taro": "3.6.16", "@types/js-cookie": "^3.0.6", "js-cookie": "^3.0.5", "react": "^18.0.0", "react-dom": "^18.0.0", "taro": "^0.0.7", "taro-ui": "^3.3.0", "vconsole": "^3.15.1", "weapp-tailwindcss": "^2.9.2"}, "devDependencies": {"@babel/core": "^7.8.0", "@iconify-json/emojione": "^1.1.7", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.16", "@tarojs/taro-loader": "3.6.16", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.16", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@unocss/preset-icons": "^0.56.4", "@unocss/webpack": "^0.56.4", "autoprefixer": "^10.4.16", "babel-preset-taro": "3.6.16", "eslint": "^8.12.0", "eslint-config-taro": "3.6.16", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "postcss": "^8.4.18", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "tailwindcss": "^3.3.3", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.1.0", "unocss": "^0.56.4", "unocss-preset-weapp": "^0.56.0", "webpack": "5.78.0"}}